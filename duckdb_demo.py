#!/usr/bin/env python3
"""
DuckDB Demo - PostgreSQL-compatible lightweight database
This script demonstrates how to use DuckDB with PostgreSQL-compatible syntax
"""

import duckdb
import os

def create_connection(db_path=None):
    """Create a DuckDB connection. If db_path is None, creates in-memory database."""
    if db_path:
        print(f"Connecting to persistent database: {db_path}")
        return duckdb.connect(db_path)
    else:
        print("Creating in-memory database")
        return duckdb.connect()

def demo_basic_operations():
    """Demonstrate basic PostgreSQL-compatible operations"""
    print("\n=== DuckDB Basic Operations Demo ===")
    
    # Create connection to a persistent database file
    conn = create_connection('demo.duckdb')
    
    try:
        # Create a table with PostgreSQL-compatible syntax
        print("\n1. Creating tables...")
        conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(150) UNIQUE,
                age INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        conn.execute("""
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                product_name VARCHAR(200),
                amount DECIMAL(10,2),
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insert sample data
        print("2. Inserting sample data...")
        conn.execute("""
            INSERT OR IGNORE INTO users (id, name, email, age) VALUES
            (1, 'John Doe', '<EMAIL>', 30),
            (2, 'Jane Smith', '<EMAIL>', 25),
            (3, 'Bob Johnson', '<EMAIL>', 35)
        """)

        conn.execute("""
            INSERT OR IGNORE INTO orders (id, user_id, product_name, amount) VALUES
            (1, 1, 'Laptop', 999.99),
            (2, 1, 'Mouse', 29.99),
            (3, 2, 'Keyboard', 79.99),
            (4, 3, 'Monitor', 299.99)
        """)
        
        # Query data with PostgreSQL-compatible syntax
        print("3. Querying data...")
        
        # Simple SELECT
        result = conn.execute("SELECT * FROM users ORDER BY name").fetchall()
        print("\nUsers:")
        for row in result:
            print(f"  ID: {row[0]}, Name: {row[1]}, Email: {row[2]}, Age: {row[3]}")
        
        # JOIN query
        result = conn.execute("""
            SELECT u.name, u.email, o.product_name, o.amount
            FROM users u
            JOIN orders o ON u.id = o.user_id
            ORDER BY u.name, o.amount DESC
        """).fetchall()
        
        print("\nUser Orders:")
        for row in result:
            print(f"  {row[0]} ({row[1]}) ordered {row[2]} for ${row[3]}")
        
        # Aggregate query
        result = conn.execute("""
            SELECT u.name, COUNT(o.id) as order_count, SUM(o.amount) as total_spent
            FROM users u
            LEFT JOIN orders o ON u.id = o.user_id
            GROUP BY u.id, u.name
            ORDER BY total_spent DESC NULLS LAST
        """).fetchall()
        
        print("\nUser Statistics:")
        for row in result:
            total = row[2] if row[2] else 0
            print(f"  {row[0]}: {row[1]} orders, ${total:.2f} total")
        
        # Advanced PostgreSQL features
        print("\n4. Advanced PostgreSQL-compatible features...")
        
        # Window functions
        result = conn.execute("""
            SELECT 
                name,
                amount,
                ROW_NUMBER() OVER (ORDER BY amount DESC) as rank,
                SUM(amount) OVER (ORDER BY amount DESC) as running_total
            FROM users u
            JOIN orders o ON u.id = o.user_id
            ORDER BY amount DESC
        """).fetchall()
        
        print("\nOrder Rankings:")
        for row in result:
            print(f"  Rank {row[2]}: {row[0]} - ${row[1]:.2f} (Running total: ${row[3]:.2f})")
        
        # Common Table Expressions (CTEs)
        result = conn.execute("""
            WITH user_stats AS (
                SELECT 
                    u.id,
                    u.name,
                    COUNT(o.id) as order_count,
                    AVG(o.amount) as avg_order_value
                FROM users u
                LEFT JOIN orders o ON u.id = o.user_id
                GROUP BY u.id, u.name
            )
            SELECT 
                name,
                order_count,
                ROUND(avg_order_value, 2) as avg_order_value,
                CASE 
                    WHEN avg_order_value > 200 THEN 'High Value'
                    WHEN avg_order_value > 50 THEN 'Medium Value'
                    ELSE 'Low Value'
                END as customer_segment
            FROM user_stats
            ORDER BY avg_order_value DESC NULLS LAST
        """).fetchall()
        
        print("\nCustomer Segmentation:")
        for row in result:
            avg_val = row[2] if row[2] else 0
            print(f"  {row[0]}: {row[1]} orders, ${avg_val:.2f} avg, {row[3]} customer")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

def demo_data_import_export():
    """Demonstrate data import/export capabilities"""
    print("\n=== Data Import/Export Demo ===")
    
    conn = create_connection('demo.duckdb')
    
    try:
        # Export to CSV
        print("1. Exporting data to CSV...")
        conn.execute("""
            COPY (
                SELECT u.name, u.email, u.age, o.product_name, o.amount
                FROM users u
                JOIN orders o ON u.id = o.user_id
            ) TO 'user_orders.csv' WITH (HEADER, DELIMITER ',')
        """)
        
        # Export to JSON
        print("2. Exporting data to JSON...")
        conn.execute("""
            COPY (
                SELECT json_object(
                    'name', u.name,
                    'email', u.email,
                    'orders', json_group_array(
                        json_object('product', o.product_name, 'amount', o.amount)
                    )
                ) as user_data
                FROM users u
                LEFT JOIN orders o ON u.id = o.user_id
                GROUP BY u.id, u.name, u.email
            ) TO 'users.json'
        """)
        
        print("3. Files exported successfully!")
        
        # Show file contents
        if os.path.exists('user_orders.csv'):
            print("\nCSV file contents:")
            with open('user_orders.csv', 'r') as f:
                print(f.read())
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("DuckDB PostgreSQL-Compatible Database Demo")
    print("=" * 50)
    
    demo_basic_operations()
    demo_data_import_export()
    
    print("\n" + "=" * 50)
    print("Demo completed! Check the generated files:")
    print("- demo.duckdb (database file)")
    print("- user_orders.csv (exported data)")
    print("- users.json (exported JSON)")
