-- PostgreSQL-Compatible Commands for DuckDB
-- This file demonstrates PostgreSQL syntax that works in DuckDB

-- ============================================
-- DATABASE AND SCHEMA OPERATIONS
-- ============================================

-- Create a new database (in DuckDB, this creates a new file)
-- Note: In CLI, use: duckdb my_database.duckdb

-- Create schemas
CREATE SCHEMA IF NOT EXISTS sales;
CREATE SCHEMA IF NOT EXISTS inventory;

-- ============================================
-- TABLE CREATION WITH POSTGRESQL FEATURES
-- ============================================

-- Create table with various PostgreSQL data types
CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    company_name VARCHAR(200) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(150) UNIQUE,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100) DEFAULT 'USA',
    credit_limit DECIMAL(12,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create table with foreign key constraints
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY,
    sku VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INTEGER,
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    stock_quantity INTEGER DEFAULT 0 CHECK (stock_quantity >= 0),
    reorder_level INTEGER DEFAULT 10,
    is_discontinued BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table with multiple constraints
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INTEGER NOT NULL REFERENCES customers(id),
    order_date DATE DEFAULT CURRENT_DATE,
    required_date DATE,
    shipped_date DATE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create order details table
CREATE TABLE IF NOT EXISTS order_details (
    id INTEGER PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    discount DECIMAL(5,4) DEFAULT 0.0000,
    line_total DECIMAL(12,2) AS (quantity * unit_price * (1 - discount))
);

-- ============================================
-- INDEXES FOR PERFORMANCE
-- ============================================

CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_company ON customers(company_name);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_order_details_order ON order_details(order_id);
CREATE INDEX IF NOT EXISTS idx_order_details_product ON order_details(product_id);

-- ============================================
-- SAMPLE DATA INSERTION
-- ============================================

-- Insert customers with INSERT OR IGNORE handling
INSERT OR IGNORE INTO customers (id, customer_code, company_name, contact_person, email, phone, city, country, credit_limit) VALUES
(1, 'CUST001', 'Tech Solutions Inc', 'John Smith', '<EMAIL>', '555-0101', 'New York', 'USA', 50000.00),
(2, 'CUST002', 'Global Enterprises', 'Sarah Johnson', '<EMAIL>', '555-0102', 'Los Angeles', 'USA', 75000.00),
(3, 'CUST003', 'Innovation Labs', 'Mike Wilson', '<EMAIL>', '555-0103', 'Chicago', 'USA', 30000.00),
(4, 'CUST004', 'Future Systems', 'Lisa Brown', '<EMAIL>', '555-0104', 'Houston', 'USA', 60000.00);

-- Insert products
INSERT OR IGNORE INTO products (id, sku, product_name, description, unit_price, stock_quantity, reorder_level) VALUES
(1, 'LAPTOP001', 'Business Laptop Pro', 'High-performance laptop for business use', 1299.99, 50, 10),
(2, 'MOUSE001', 'Wireless Optical Mouse', 'Ergonomic wireless mouse', 29.99, 200, 50),
(3, 'KEYB001', 'Mechanical Keyboard', 'RGB mechanical gaming keyboard', 149.99, 75, 15),
(4, 'MONITOR001', '27" 4K Monitor', 'Ultra HD 4K display monitor', 399.99, 30, 5),
(5, 'TABLET001', 'Business Tablet', '10-inch tablet for business', 599.99, 40, 8);

-- Insert orders
INSERT OR IGNORE INTO orders (id, order_number, customer_id, order_date, required_date, status, notes) VALUES
(1, 'ORD-2024-001', 1, '2024-01-15', '2024-01-25', 'delivered', 'Rush order'),
(2, 'ORD-2024-002', 2, '2024-01-16', '2024-01-30', 'shipped', 'Standard delivery'),
(3, 'ORD-2024-003', 1, '2024-01-18', '2024-02-01', 'processing', 'Large order'),
(4, 'ORD-2024-004', 3, '2024-01-20', '2024-02-05', 'pending', 'Awaiting approval'),
(5, 'ORD-2024-005', 4, '2024-01-22', '2024-02-10', 'shipped', 'Express shipping');

-- Insert order details
INSERT OR IGNORE INTO order_details (id, order_id, product_id, quantity, unit_price, discount) VALUES
(1, 1, 1, 2, 1299.99, 0.05),  -- 5% discount
(2, 1, 2, 4, 29.99, 0.00),
(3, 2, 3, 1, 149.99, 0.00),
(4, 2, 4, 2, 399.99, 0.10),   -- 10% discount
(5, 3, 1, 5, 1299.99, 0.15),  -- 15% bulk discount
(6, 3, 5, 3, 599.99, 0.05),
(7, 4, 2, 10, 29.99, 0.20),   -- 20% volume discount
(8, 5, 4, 1, 399.99, 0.00);

-- ============================================
-- ADVANCED POSTGRESQL QUERIES
-- ============================================

-- Update order totals using subquery
UPDATE orders 
SET total_amount = (
    SELECT COALESCE(SUM(line_total), 0)
    FROM order_details 
    WHERE order_id = orders.id
);

-- Complex query with multiple JOINs and window functions
SELECT 
    c.company_name,
    c.city,
    o.order_number,
    o.order_date,
    o.total_amount,
    ROW_NUMBER() OVER (PARTITION BY c.id ORDER BY o.order_date DESC) as order_rank,
    SUM(o.total_amount) OVER (PARTITION BY c.id) as customer_total,
    AVG(o.total_amount) OVER (PARTITION BY c.city) as city_avg_order
FROM customers c
JOIN orders o ON c.id = o.customer_id
WHERE o.status IN ('shipped', 'delivered')
ORDER BY c.company_name, o.order_date DESC;

-- Common Table Expression (CTE) example
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', o.order_date) as month,
        COUNT(*) as order_count,
        SUM(o.total_amount) as monthly_revenue,
        AVG(o.total_amount) as avg_order_value
    FROM orders o
    WHERE o.status IN ('shipped', 'delivered')
    GROUP BY DATE_TRUNC('month', o.order_date)
),
product_performance AS (
    SELECT 
        p.product_name,
        SUM(od.quantity) as total_sold,
        SUM(od.line_total) as total_revenue,
        COUNT(DISTINCT od.order_id) as orders_count
    FROM products p
    JOIN order_details od ON p.id = od.product_id
    JOIN orders o ON od.order_id = o.id
    WHERE o.status IN ('shipped', 'delivered')
    GROUP BY p.id, p.product_name
)
SELECT 
    ms.month,
    ms.order_count,
    ms.monthly_revenue,
    ms.avg_order_value,
    LAG(ms.monthly_revenue) OVER (ORDER BY ms.month) as prev_month_revenue,
    ROUND(
        (ms.monthly_revenue - LAG(ms.monthly_revenue) OVER (ORDER BY ms.month)) / 
        LAG(ms.monthly_revenue) OVER (ORDER BY ms.month) * 100, 2
    ) as revenue_growth_pct
FROM monthly_sales ms
ORDER BY ms.month;

-- ============================================
-- USEFUL POSTGRESQL FUNCTIONS IN DUCKDB
-- ============================================

-- String functions
SELECT 
    customer_code,
    UPPER(company_name) as company_upper,
    LENGTH(company_name) as name_length,
    SUBSTRING(email FROM 1 FOR POSITION('@' IN email) - 1) as username,
    SPLIT_PART(email, '@', 2) as domain
FROM customers
LIMIT 5;

-- Date functions
SELECT 
    order_number,
    order_date,
    EXTRACT(YEAR FROM order_date) as order_year,
    EXTRACT(MONTH FROM order_date) as order_month,
    EXTRACT(DOW FROM order_date) as day_of_week,
    AGE(CURRENT_DATE, order_date) as days_since_order
FROM orders
ORDER BY order_date DESC;

-- Aggregate functions with FILTER
SELECT 
    c.city,
    COUNT(*) as total_customers,
    COUNT(*) FILTER (WHERE c.is_active = TRUE) as active_customers,
    AVG(c.credit_limit) as avg_credit_limit,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY c.credit_limit) as median_credit_limit
FROM customers c
GROUP BY c.city
ORDER BY total_customers DESC;
