#!/usr/bin/env python3
"""
Test DuckDB CLI functionality and demonstrate PostgreSQL-compatible commands
"""

import duckdb
import subprocess
import os

def test_cli_commands():
    """Test basic CLI commands using Python subprocess"""
    print("=== Testing DuckDB CLI Commands ===\n")
    
    # Create a test database with some data
    conn = duckdb.connect('test_cli.duckdb')
    
    # Create and populate test table
    conn.execute("""
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY,
            name VARCHAR(100),
            department VARCHAR(50),
            salary DECIMAL(10,2),
            hire_date DATE
        )
    """)
    
    conn.execute("""
        INSERT OR IGNORE INTO employees VALUES
        (1, '<PERSON>', 'Engineering', 85000.00, '2022-01-15'),
        (2, '<PERSON>', 'Marketing', 65000.00, '2022-03-20'),
        (3, '<PERSON>', 'Engineering', 92000.00, '2021-11-10'),
        (4, '<PERSON>', 'Sales', 58000.00, '2023-02-01'),
        (5, '<PERSON>', 'HR', 72000.00, '2022-08-15')
    """)
    
    conn.close()
    print("✓ Test database created with sample data")
    
    # Test various SQL commands
    test_queries = [
        ("Basic SELECT", "SELECT * FROM employees ORDER BY name;"),
        ("Aggregate Query", "SELECT department, COUNT(*) as emp_count, AVG(salary) as avg_salary FROM employees GROUP BY department ORDER BY avg_salary DESC;"),
        ("Date Functions", "SELECT name, hire_date, EXTRACT(YEAR FROM hire_date) as hire_year FROM employees ORDER BY hire_date;"),
        ("Window Functions", "SELECT name, department, salary, RANK() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank FROM employees ORDER BY department, dept_rank;")
    ]
    
    for description, query in test_queries:
        print(f"\n--- {description} ---")
        print(f"Query: {query}")
        print("Result:")
        
        # Execute query using Python DuckDB
        conn = duckdb.connect('test_cli.duckdb')
        try:
            result = conn.execute(query).fetchall()
            for row in result:
                print(f"  {row}")
        except Exception as e:
            print(f"  Error: {e}")
        finally:
            conn.close()

def demonstrate_postgresql_features():
    """Demonstrate PostgreSQL-compatible features"""
    print("\n=== PostgreSQL-Compatible Features Demo ===\n")
    
    conn = duckdb.connect('postgres_demo.duckdb')
    
    try:
        # Create tables with PostgreSQL-style constraints
        print("1. Creating tables with PostgreSQL constraints...")
        conn.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                description TEXT
            )
        """)
        
        conn.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name VARCHAR(200) NOT NULL,
                category_id INTEGER REFERENCES categories(id),
                price DECIMAL(10,2) CHECK (price > 0),
                in_stock BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insert data
        print("2. Inserting sample data...")
        conn.execute("""
            INSERT OR IGNORE INTO categories VALUES
            (1, 'Electronics', 'Electronic devices and gadgets'),
            (2, 'Books', 'Physical and digital books'),
            (3, 'Clothing', 'Apparel and accessories')
        """)
        
        conn.execute("""
            INSERT OR IGNORE INTO products (id, name, category_id, price) VALUES
            (1, 'Smartphone', 1, 699.99),
            (2, 'Laptop', 1, 1299.99),
            (3, 'Programming Book', 2, 49.99),
            (4, 'T-Shirt', 3, 19.99),
            (5, 'Headphones', 1, 199.99)
        """)
        
        # PostgreSQL-style queries
        print("3. PostgreSQL-compatible queries...")
        
        # CASE statement
        result = conn.execute("""
            SELECT 
                p.name,
                p.price,
                CASE 
                    WHEN p.price > 500 THEN 'Expensive'
                    WHEN p.price > 100 THEN 'Moderate'
                    ELSE 'Affordable'
                END as price_category
            FROM products p
            ORDER BY p.price DESC
        """).fetchall()
        
        print("\nPrice Categories:")
        for row in result:
            print(f"  {row[0]}: ${row[1]:.2f} ({row[2]})")
        
        # JOIN with aggregation
        result = conn.execute("""
            SELECT 
                c.name as category,
                COUNT(p.id) as product_count,
                AVG(p.price) as avg_price,
                MIN(p.price) as min_price,
                MAX(p.price) as max_price
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id
            GROUP BY c.id, c.name
            ORDER BY avg_price DESC NULLS LAST
        """).fetchall()
        
        print("\nCategory Statistics:")
        for row in result:
            avg_price = row[2] if row[2] else 0
            min_price = row[3] if row[3] else 0
            max_price = row[4] if row[4] else 0
            print(f"  {row[0]}: {row[1]} products, ${avg_price:.2f} avg (${min_price:.2f}-${max_price:.2f})")
        
        # String functions
        result = conn.execute("""
            SELECT 
                name,
                LENGTH(name) as name_length,
                UPPER(name) as name_upper,
                SUBSTRING(name FROM 1 FOR 3) as name_prefix
            FROM products
            ORDER BY name_length DESC
        """).fetchall()
        
        print("\nString Functions:")
        for row in result:
            print(f"  {row[0]} ({row[1]} chars): {row[2]} -> {row[3]}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

def show_database_files():
    """Show created database files"""
    print("\n=== Created Database Files ===")
    
    db_files = [f for f in os.listdir('.') if f.endswith('.duckdb')]
    
    if db_files:
        print("Database files created:")
        for db_file in db_files:
            size = os.path.getsize(db_file)
            print(f"  {db_file} ({size:,} bytes)")
    else:
        print("No database files found.")
    
    # Show other generated files
    other_files = ['user_orders.csv', 'users.json']
    print("\nOther generated files:")
    for file in other_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  {file} ({size:,} bytes)")

if __name__ == "__main__":
    print("DuckDB PostgreSQL-Compatible Database Testing")
    print("=" * 60)
    
    test_cli_commands()
    demonstrate_postgresql_features()
    show_database_files()
    
    print("\n" + "=" * 60)
    print("Testing completed!")
    print("\nTo use DuckDB CLI interactively:")
    print("1. Open a new terminal/command prompt")
    print("2. Navigate to this directory")
    print("3. Run: duckdb test_cli.duckdb")
    print("4. Try queries like: SELECT * FROM employees;")
