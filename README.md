# DuckDB - PostgreSQL-Compatible Lightweight Database

DuckDB is an embedded analytical database that supports PostgreSQL-compatible SQL syntax. It's perfect for development, testing, and analytical workloads without requiring a full PostgreSQL server installation.

## 🚀 Quick Start

### Installation
DuckDB is already installed in this workspace:
- **Python package**: `duckdb` (for programmatic access)
- **CLI tool**: `duckdb` command (for interactive use)

### Basic Usage

#### 1. Command Line Interface
```bash
# Start DuckDB CLI with a persistent database file
duckdb my_database.duckdb

# Start DuckDB CLI with in-memory database
duckdb

# Execute SQL file
duckdb my_database.duckdb < postgresql_commands.sql

# Execute single command
duckdb my_database.duckdb "SELECT * FROM customers;"
```

#### 2. Python Integration
```python
import duckdb

# Connect to persistent database
conn = duckdb.connect('my_database.duckdb')

# Connect to in-memory database
conn = duckdb.connect()

# Execute queries
result = conn.execute("SELECT * FROM customers").fetchall()
conn.close()
```

## 📁 Files in This Workspace

### `duckdb_demo.py`
Comprehensive Python demonstration showing:
- Database connection and table creation
- PostgreSQL-compatible SQL operations
- Advanced features (JOINs, CTEs, window functions)
- Data import/export capabilities

### `postgresql_commands.sql`
Complete SQL reference with PostgreSQL-compatible syntax:
- Table creation with constraints and indexes
- Sample data insertion
- Complex queries with JOINs and subqueries
- Advanced PostgreSQL functions

## 🔧 PostgreSQL Features Supported

### Data Types
- `SERIAL` (auto-incrementing integers)
- `VARCHAR`, `TEXT`, `CHAR`
- `INTEGER`, `BIGINT`, `DECIMAL`, `NUMERIC`
- `BOOLEAN`
- `DATE`, `TIMESTAMP`, `TIME`
- `JSON`, `JSONB`

### SQL Features
- **Constraints**: PRIMARY KEY, FOREIGN KEY, UNIQUE, CHECK, NOT NULL
- **Indexes**: CREATE INDEX with various options
- **Joins**: INNER, LEFT, RIGHT, FULL OUTER
- **Subqueries**: Correlated and non-correlated
- **CTEs**: Common Table Expressions with WITH clause
- **Window Functions**: ROW_NUMBER(), RANK(), SUM() OVER, etc.
- **Aggregate Functions**: GROUP BY, HAVING, FILTER clause
- **String Functions**: SUBSTRING, SPLIT_PART, LENGTH, etc.
- **Date Functions**: EXTRACT, DATE_TRUNC, AGE, etc.

### Advanced Features
- **UPSERT**: INSERT ... ON CONFLICT DO NOTHING/UPDATE
- **Generated Columns**: GENERATED ALWAYS AS ... STORED
- **Array Operations**: Basic array support
- **JSON Operations**: JSON extraction and manipulation

## 🎯 Common Use Cases

### 1. Development and Testing
```sql
-- Create test database
-- In CLI: duckdb test.duckdb

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(150) UNIQUE
);

INSERT INTO users (name, email) VALUES 
('John Doe', '<EMAIL>'),
('Jane Smith', '<EMAIL>');

SELECT * FROM users;
```

### 2. Data Analysis
```sql
-- Analyze CSV data directly
SELECT * FROM read_csv_auto('data.csv');

-- Create table from CSV
CREATE TABLE sales AS SELECT * FROM read_csv_auto('sales.csv');

-- Perform analytics
SELECT 
    product_category,
    SUM(amount) as total_sales,
    AVG(amount) as avg_sale,
    COUNT(*) as transaction_count
FROM sales
GROUP BY product_category
ORDER BY total_sales DESC;
```

### 3. Data Migration Testing
```sql
-- Test PostgreSQL queries before production deployment
-- All queries in postgresql_commands.sql work in DuckDB

-- Complex reporting query
WITH monthly_revenue AS (
    SELECT 
        DATE_TRUNC('month', order_date) as month,
        SUM(total_amount) as revenue
    FROM orders
    GROUP BY DATE_TRUNC('month', order_date)
)
SELECT 
    month,
    revenue,
    LAG(revenue) OVER (ORDER BY month) as prev_month,
    revenue - LAG(revenue) OVER (ORDER BY month) as growth
FROM monthly_revenue
ORDER BY month;
```

## 🚀 Getting Started Commands

### Run the Demo
```bash
# Execute the Python demo
python duckdb_demo.py

# Execute SQL commands
duckdb demo.duckdb < postgresql_commands.sql

# Interactive CLI session
duckdb demo.duckdb
```

### Create Your First Database
```bash
# Start CLI with new database
duckdb my_project.duckdb

# In the CLI, create a table
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

# Insert some data
INSERT INTO tasks (title, description) VALUES 
('Learn DuckDB', 'Explore PostgreSQL-compatible features'),
('Build project', 'Create application using DuckDB');

# Query the data
SELECT * FROM tasks ORDER BY created_at DESC;
```

## 📊 Performance Benefits

- **Fast**: Optimized for analytical queries
- **Lightweight**: Single executable, no server required
- **Compatible**: PostgreSQL syntax works out of the box
- **Scalable**: Handles large datasets efficiently
- **Portable**: Database files are self-contained

## 🔄 Migration from PostgreSQL

Most PostgreSQL queries work directly in DuckDB. Key differences:
- No need for server setup or configuration
- Some advanced PostgreSQL extensions not available
- Optimized for analytical workloads rather than OLTP
- File-based storage instead of server-based

## 📚 Next Steps

1. **Run the demo**: `python duckdb_demo.py`
2. **Explore SQL**: Open `postgresql_commands.sql` in your editor
3. **Start CLI**: `duckdb my_database.duckdb`
4. **Build your project**: Use DuckDB as your development database

DuckDB provides the perfect balance of PostgreSQL compatibility and lightweight deployment for your database needs!
