# DuckDB Quick Reference - PostgreSQL Compatible Commands

## 🚀 Getting Started

### Connect to Database
```bash
# Create/connect to persistent database
duckdb my_database.duckdb

# In-memory database
duckdb

# Execute SQL file (use cmd on Windows)
cmd /c "duckdb my_database.duckdb < script.sql"
```

### Python Usage
```python
import duckdb

# Connect to database
conn = duckdb.connect('my_database.duckdb')  # persistent
conn = duckdb.connect()                      # in-memory

# Execute queries
result = conn.execute("SELECT * FROM table").fetchall()
conn.close()
```

## 📊 Basic Operations

### Create Tables
```sql
-- Basic table
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- With constraints
CREATE TABLE orders (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    amount DECIMAL(10,2) CHECK (amount > 0),
    status VARCHAR(20) DEFAULT 'pending'
);
```

### Insert Data
```sql
-- Basic insert
INSERT INTO users (name, email) VALUES 
('John Doe', '<EMAIL>'),
('Jane Smith', '<EMAIL>');

-- Insert or ignore (DuckDB syntax)
INSERT OR IGNORE INTO users VALUES 
(1, 'John Doe', '<EMAIL>', CURRENT_TIMESTAMP);

-- PostgreSQL-style upsert (limited support)
INSERT INTO users (name, email) VALUES ('John', '<EMAIL>')
ON CONFLICT (email) DO NOTHING;
```

### Query Data
```sql
-- Basic SELECT
SELECT * FROM users WHERE name LIKE 'John%';

-- Joins
SELECT u.name, o.amount 
FROM users u 
JOIN orders o ON u.id = o.user_id;

-- Aggregation
SELECT status, COUNT(*), AVG(amount) 
FROM orders 
GROUP BY status 
HAVING COUNT(*) > 1;
```

## 🔧 Advanced Features

### Window Functions
```sql
SELECT 
    name,
    amount,
    ROW_NUMBER() OVER (ORDER BY amount DESC) as rank,
    SUM(amount) OVER (ORDER BY amount DESC) as running_total
FROM orders;
```

### Common Table Expressions (CTEs)
```sql
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', order_date) as month,
        SUM(amount) as total
    FROM orders
    GROUP BY DATE_TRUNC('month', order_date)
)
SELECT month, total, LAG(total) OVER (ORDER BY month) as prev_month
FROM monthly_sales;
```

### String Functions
```sql
SELECT 
    name,
    LENGTH(name) as name_length,
    UPPER(name) as name_upper,
    SUBSTRING(name FROM 1 FOR 3) as prefix,
    SPLIT_PART(email, '@', 2) as domain
FROM users;
```

### Date Functions
```sql
SELECT 
    order_date,
    EXTRACT(YEAR FROM order_date) as year,
    EXTRACT(MONTH FROM order_date) as month,
    DATE_TRUNC('month', order_date) as month_start,
    AGE(CURRENT_DATE, order_date) as days_ago
FROM orders;
```

## 📁 Data Import/Export

### CSV Operations
```sql
-- Import CSV
CREATE TABLE sales AS SELECT * FROM read_csv_auto('sales.csv');

-- Export to CSV
COPY (SELECT * FROM users) TO 'users.csv' WITH (HEADER, DELIMITER ',');
```

### JSON Operations
```sql
-- Export to JSON
COPY (
    SELECT json_object('name', name, 'email', email) as user_data
    FROM users
) TO 'users.json';

-- Read JSON
SELECT * FROM read_json_auto('data.json');
```

## 🎯 Common Patterns

### Pagination
```sql
SELECT * FROM users 
ORDER BY name 
LIMIT 10 OFFSET 20;
```

### Conditional Logic
```sql
SELECT 
    name,
    CASE 
        WHEN amount > 1000 THEN 'High'
        WHEN amount > 100 THEN 'Medium'
        ELSE 'Low'
    END as category
FROM orders;
```

### Aggregation with Filtering
```sql
SELECT 
    status,
    COUNT(*) as total_orders,
    COUNT(*) FILTER (WHERE amount > 100) as high_value_orders,
    AVG(amount) as avg_amount
FROM orders
GROUP BY status;
```

## 🔍 Useful Queries

### Table Information
```sql
-- List all tables
SHOW TABLES;

-- Describe table structure
DESCRIBE users;

-- Table statistics
SELECT COUNT(*) FROM users;
```

### Performance
```sql
-- Create index
CREATE INDEX idx_users_email ON users(email);

-- Analyze query performance
EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';
```

## 🚨 Key Differences from PostgreSQL

### Syntax Differences
- Use `INTEGER PRIMARY KEY` instead of `SERIAL PRIMARY KEY`
- Use `INSERT OR IGNORE` instead of `INSERT ... ON CONFLICT DO NOTHING`
- Generated columns: `AS (expression)` instead of `GENERATED ALWAYS AS ... STORED`

### Supported PostgreSQL Features
✅ Most SQL standard features
✅ Window functions
✅ CTEs (Common Table Expressions)
✅ JSON operations
✅ Array operations (basic)
✅ String and date functions
✅ Subqueries and joins

### Not Supported
❌ Full PostgreSQL extensions
❌ Stored procedures/functions
❌ Triggers
❌ Custom data types
❌ Full-text search (built-in)

## 💡 Tips

1. **Development**: Use DuckDB for local development and testing
2. **Analytics**: Excellent for data analysis and reporting
3. **Migration**: Test PostgreSQL queries in DuckDB before production
4. **Performance**: Create indexes on frequently queried columns
5. **Data Types**: Stick to standard SQL types for best compatibility

## 🔗 Quick Commands

```bash
# Start interactive session
duckdb my_database.duckdb

# Execute single query
duckdb my_database.duckdb "SELECT COUNT(*) FROM users;"

# Import and query CSV
duckdb "SELECT * FROM read_csv_auto('data.csv') LIMIT 5;"

# Export query results
duckdb my_database.duckdb "COPY (SELECT * FROM users) TO 'export.csv';"
```
